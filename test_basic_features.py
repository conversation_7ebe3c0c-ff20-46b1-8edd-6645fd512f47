#!/usr/bin/env python3
"""
基础功能测试脚本
Basic Features Test Script
测试核心动画功能（不依赖外部库）
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class BasicFeaturesTest:
    """基础功能测试类"""
    
    def __init__(self):
        self.test_results = {}
        
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 Starting Basic Features Test Suite")
        print("=" * 50)
        
        tests = [
            ("Animation Presets Library", self.test_animation_presets),
            ("Transition Optimizer", self.test_transition_optimizer),
            ("Quality Checker", self.test_quality_checker),
            ("FBX Validator", self.test_fbx_validator),
            ("Configuration System", self.test_configuration),
        ]
        
        for test_name, test_func in tests:
            print(f"🧪 Testing {test_name}...")
            try:
                result = await test_func()
                self.test_results[test_name] = {"status": "PASS", "result": result}
                print(f"✅ {test_name} - PASSED")
            except Exception as e:
                self.test_results[test_name] = {"status": "FAIL", "error": str(e)}
                print(f"❌ {test_name} - FAILED: {e}")
        
        self.generate_test_report()
    
    async def test_animation_presets(self):
        """测试动画预设库"""
        try:
            from backend.animation.animation_presets import AnimationPresetsLibrary
            
            presets_library = AnimationPresetsLibrary()
            
            # 测试获取所有分类
            categories = presets_library.get_all_categories()
            assert len(categories) > 0, "No categories found"
            
            # 测试搜索功能
            search_results = presets_library.search_presets(["walk", "run"])
            assert len(search_results) > 0, "No search results found"
            
            # 测试按分类获取
            locomotion_presets = presets_library.get_presets_by_category("locomotion")
            assert len(locomotion_presets) > 0, "No locomotion presets found"
            
            # 测试API导出
            api_presets = presets_library.export_presets_for_api()
            assert isinstance(api_presets, dict), "API presets not a dict"
            
            return {
                "total_categories": len(categories),
                "total_presets": len(presets_library.presets),
                "search_results": len(search_results),
                "locomotion_presets": len(locomotion_presets)
            }
            
        except ImportError as e:
            return {"error": f"Import failed: {e}", "status": "skipped"}
    
    async def test_transition_optimizer(self):
        """测试动画过渡优化器"""
        try:
            from backend.animation.transition_optimizer import TransitionOptimizer
            
            optimizer = TransitionOptimizer()
            
            # 创建测试动作序列
            test_actions = [
                {"type": "idle", "duration": 2.0},
                {"type": "walk", "duration": 3.0},
                {"type": "run", "duration": 2.0},
                {"type": "jump", "duration": 1.5}
            ]
            
            # 测试序列优化
            optimized_sequence = optimizer.optimize_action_sequence(test_actions)
            assert len(optimized_sequence) >= len(test_actions), "Optimized sequence too short"
            
            # 测试时间优化
            timing_optimized = optimizer.optimize_timing(test_actions)
            assert len(timing_optimized) == len(test_actions), "Timing optimization changed action count"
            
            # 测试过渡建议
            recommendations = optimizer.get_transition_recommendations("walk", "run")
            assert "compatibility_score" in recommendations, "Missing compatibility score"
            
            return {
                "original_actions": len(test_actions),
                "optimized_actions": len(optimized_sequence),
                "compatibility_score": recommendations["compatibility_score"],
                "recommended_type": recommendations["recommended_type"]
            }
            
        except ImportError as e:
            return {"error": f"Import failed: {e}", "status": "skipped"}
    
    async def test_quality_checker(self):
        """测试动画质量检查器"""
        try:
            from backend.animation.quality_checker import AnimationQualityChecker
            
            checker = AnimationQualityChecker()
            
            # 创建测试动画数据
            test_animation_data = {
                "action_sequence": {
                    "actions": [
                        {"type": "walk", "duration": 2.0},
                        {"type": "run", "duration": 1.5},
                        {"type": "jump", "duration": 1.8}
                    ],
                    "frame_rate": 30,
                    "total_duration": 5.3
                }
            }
            
            quality_result = checker.check_animation_quality(test_animation_data)
            
            # 验证结果结构
            assert "overall_score" in quality_result, "Missing overall score"
            assert "quality_level" in quality_result, "Missing quality level"
            assert "metrics" in quality_result, "Missing metrics"
            
            return {
                "overall_score": quality_result["overall_score"],
                "quality_level": quality_result["quality_level"],
                "issues_count": len(quality_result.get("issues", [])),
                "recommendations_count": len(quality_result.get("recommendations", []))
            }
            
        except ImportError as e:
            return {"error": f"Import failed: {e}", "status": "skipped"}
    
    async def test_fbx_validator(self):
        """测试FBX验证器"""
        try:
            from backend.animation.fbx_validator import FBXValidator
            
            validator = FBXValidator()
            
            # 创建测试FBX文件（模拟）
            test_file = "output/animations/test_animation.fbx"
            os.makedirs("output/animations", exist_ok=True)
            
            # 创建一个简单的测试文件
            with open(test_file, "wb") as f:
                f.write(b"Kaydara FBX Binary  \x00\x1a\x00")  # FBX文件头
                f.write(b"\x00" * 100)  # 填充数据
            
            try:
                validation_result = validator.validate_fbx_file(test_file)
                
                # 验证结果结构
                assert "is_valid" in validation_result, "Missing is_valid field"
                assert "file_exists" in validation_result, "Missing file_exists field"
                
                # 生成报告
                report = validator.generate_validation_report(validation_result)
                assert len(report) > 0, "Empty validation report"
                
                return {
                    "validation_passed": True,
                    "file_size": validation_result.get("file_size", 0),
                    "compatibility_checks": len(validation_result.get("compatibility", {})),
                    "recommendations_count": len(validation_result.get("recommendations", []))
                }
                
            finally:
                # 清理测试文件
                if os.path.exists(test_file):
                    os.remove(test_file)
                    
        except ImportError as e:
            return {"error": f"Import failed: {e}", "status": "skipped"}
    
    async def test_configuration(self):
        """测试配置系统"""
        try:
            from backend.config.enhanced_features_config import (
                get_enhanced_config,
                validate_config,
                EnhancedFeaturesConfig
            )
            
            # 测试配置加载
            config = get_enhanced_config()
            assert isinstance(config, EnhancedFeaturesConfig), "Config not loaded properly"
            
            # 测试配置验证
            validation_result = validate_config()
            assert isinstance(validation_result, bool), "Validation result not boolean"
            
            # 测试配置属性
            assert hasattr(config, 'enable_fbx_validation'), "Missing fbx_validation config"
            assert hasattr(config, 'enable_quality_checking'), "Missing quality_checking config"
            assert hasattr(config, 'output_dir'), "Missing output_dir config"
            
            return {
                "config_loaded": True,
                "validation_passed": validation_result,
                "fbx_validation_enabled": config.enable_fbx_validation,
                "quality_checking_enabled": config.enable_quality_checking,
                "output_dir": config.output_dir
            }
            
        except ImportError as e:
            return {"error": f"Import failed: {e}", "status": "skipped"}
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📊 Generating Test Report...")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result["status"] == "PASS")
        failed_tests = total_tests - passed_tests
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": f"{(passed_tests/total_tests)*100:.1f}%"
            },
            "details": self.test_results
        }
        
        # 保存报告到文件
        report_file = "basic_test_results.json"
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        print("\n📋 Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {failed_tests}")
        print(f"   Success Rate: {report['summary']['success_rate']}")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for test_name, result in self.test_results.items():
                if result["status"] == "FAIL":
                    print(f"   - {test_name}: {result['error']}")
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        return report


async def main():
    """主函数"""
    print("🎬 Motion Agent Basic Features Test")
    print("=" * 50)
    
    # 确保必要的目录存在
    os.makedirs("output/animations", exist_ok=True)
    os.makedirs("temp/animation_data", exist_ok=True)
    os.makedirs("backend/config", exist_ok=True)
    
    tester = BasicFeaturesTest()
    await tester.run_all_tests()
    
    print("=" * 50)
    print("🏁 Test Suite Completed")


if __name__ == "__main__":
    asyncio.run(main())
