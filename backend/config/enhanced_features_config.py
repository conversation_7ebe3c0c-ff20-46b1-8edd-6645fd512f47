"""
增强功能配置
Enhanced Features Configuration
"""

import os
from typing import Dict, Any, Optional
from pydantic import BaseSettings, Field
from loguru import logger


class FBXValidationConfig(BaseSettings):
    """FBX验证配置"""
    
    # 文件大小限制 (MB)
    max_file_size_mb: int = Field(default=100, description="Maximum FBX file size in MB")
    
    # 兼容性检查
    check_maya_compatibility: bool = Field(default=True, description="Check Maya compatibility")
    check_3dmax_compatibility: bool = Field(default=True, description="Check 3DMAX compatibility")
    check_unity_compatibility: bool = Field(default=True, description="Check Unity compatibility")
    check_unreal_compatibility: bool = Field(default=True, description="Check Unreal compatibility")
    
    # 质量阈值
    min_quality_score: float = Field(default=0.6, description="Minimum quality score")
    max_bone_count: int = Field(default=500, description="Maximum bone count")
    max_animation_duration: float = Field(default=300.0, description="Maximum animation duration in seconds")
    
    class Config:
        env_prefix = "FBX_VALIDATION_"


class QualityCheckerConfig(BaseSettings):
    """质量检查配置"""
    
    # 评分权重
    smoothness_weight: float = Field(default=0.25, description="Smoothness score weight")
    timing_weight: float = Field(default=0.20, description="Timing score weight")
    spacing_weight: float = Field(default=0.15, description="Spacing score weight")
    anticipation_weight: float = Field(default=0.15, description="Anticipation score weight")
    follow_through_weight: float = Field(default=0.15, description="Follow through score weight")
    secondary_animation_weight: float = Field(default=0.10, description="Secondary animation score weight")
    
    # 质量阈值
    excellent_threshold: float = Field(default=0.9, description="Excellent quality threshold")
    good_threshold: float = Field(default=0.8, description="Good quality threshold")
    acceptable_threshold: float = Field(default=0.6, description="Acceptable quality threshold")
    poor_threshold: float = Field(default=0.4, description="Poor quality threshold")
    
    # 动画原理检查
    check_squash_and_stretch: bool = Field(default=True, description="Check squash and stretch")
    check_anticipation: bool = Field(default=True, description="Check anticipation")
    check_staging: bool = Field(default=True, description="Check staging")
    check_follow_through: bool = Field(default=True, description="Check follow through")
    check_arcs: bool = Field(default=True, description="Check arcs")
    check_timing: bool = Field(default=True, description="Check timing")
    
    class Config:
        env_prefix = "QUALITY_CHECKER_"


class MixamoConfig(BaseSettings):
    """Mixamo集成配置"""
    
    # API配置
    api_key: Optional[str] = Field(default=None, description="Mixamo API key")
    api_base_url: str = Field(default="https://www.mixamo.com/api/v1", description="Mixamo API base URL")
    
    # 缓存配置
    cache_enabled: bool = Field(default=True, description="Enable local caching")
    cache_dir: str = Field(default="cache/mixamo", description="Cache directory")
    cache_max_age_days: int = Field(default=7, description="Cache max age in days")
    cache_max_size_mb: int = Field(default=1000, description="Cache max size in MB")
    
    # 下载配置
    download_timeout: int = Field(default=300, description="Download timeout in seconds")
    max_concurrent_downloads: int = Field(default=3, description="Max concurrent downloads")
    
    # 质量配置
    preferred_frame_rate: int = Field(default=30, description="Preferred frame rate")
    reduce_keyframes: bool = Field(default=False, description="Reduce keyframes")
    include_skin: bool = Field(default=True, description="Include skin in download")
    
    class Config:
        env_prefix = "MIXAMO_"


class RiggingCheckerConfig(BaseSettings):
    """骨骼绑定检查配置"""
    
    # 骨骼限制
    max_bones: int = Field(default=500, description="Maximum bone count")
    max_hierarchy_depth: int = Field(default=10, description="Maximum hierarchy depth")
    min_bone_length: float = Field(default=0.001, description="Minimum bone length")
    max_bone_length: float = Field(default=100.0, description="Maximum bone length")
    
    # 权重配置
    max_influences_per_vertex: int = Field(default=4, description="Max influences per vertex")
    min_weight_threshold: float = Field(default=0.001, description="Minimum weight threshold")
    weight_normalization_tolerance: float = Field(default=0.001, description="Weight normalization tolerance")
    
    # IK配置
    ideal_ik_chain_min_length: int = Field(default=2, description="Ideal IK chain minimum length")
    ideal_ik_chain_max_length: int = Field(default=4, description="Ideal IK chain maximum length")
    
    # 命名规范
    enforce_naming_conventions: bool = Field(default=True, description="Enforce naming conventions")
    require_left_right_symmetry: bool = Field(default=True, description="Require left-right symmetry")
    
    class Config:
        env_prefix = "RIGGING_CHECKER_"


class TransitionOptimizerConfig(BaseSettings):
    """过渡优化配置"""
    
    # 过渡时长范围
    min_transition_duration: float = Field(default=0.1, description="Minimum transition duration")
    max_transition_duration: float = Field(default=2.0, description="Maximum transition duration")
    
    # 兼容性阈值
    min_compatibility_score: float = Field(default=0.3, description="Minimum compatibility score for transitions")
    
    # 过渡类型偏好
    default_transition_type: str = Field(default="ease_in_out", description="Default transition type")
    default_blend_mode: str = Field(default="crossfade", description="Default blend mode")
    
    # 动画原理
    enable_anticipation: bool = Field(default=True, description="Enable anticipation frames")
    enable_follow_through: bool = Field(default=True, description="Enable follow through frames")
    enable_overlap: bool = Field(default=True, description="Enable overlapping action")
    
    # 时间优化
    optimize_timing: bool = Field(default=True, description="Enable timing optimization")
    timing_adjustment_factor: float = Field(default=0.1, description="Timing adjustment factor")
    
    class Config:
        env_prefix = "TRANSITION_OPTIMIZER_"


class AnimationPresetsConfig(BaseSettings):
    """动画预设配置"""
    
    # 预设库配置
    enable_extended_presets: bool = Field(default=True, description="Enable extended preset library")
    enable_custom_presets: bool = Field(default=True, description="Enable custom presets")
    
    # 搜索配置
    search_fuzzy_matching: bool = Field(default=True, description="Enable fuzzy matching in search")
    search_max_results: int = Field(default=50, description="Maximum search results")
    
    # 分类配置
    enable_all_categories: bool = Field(default=True, description="Enable all preset categories")
    default_difficulty_level: str = Field(default="intermediate", description="Default difficulty level")
    
    class Config:
        env_prefix = "ANIMATION_PRESETS_"


class EnhancedFeaturesConfig(BaseSettings):
    """增强功能总配置"""
    
    # 功能开关
    enable_fbx_validation: bool = Field(default=True, description="Enable FBX validation")
    enable_quality_checking: bool = Field(default=True, description="Enable quality checking")
    enable_mixamo_integration: bool = Field(default=False, description="Enable Mixamo integration")
    enable_rigging_checking: bool = Field(default=True, description="Enable rigging checking")
    enable_transition_optimization: bool = Field(default=True, description="Enable transition optimization")
    enable_extended_presets: bool = Field(default=True, description="Enable extended presets")
    
    # 性能配置
    max_concurrent_processes: int = Field(default=4, description="Maximum concurrent processes")
    processing_timeout: int = Field(default=600, description="Processing timeout in seconds")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="Log level")
    enable_detailed_logging: bool = Field(default=True, description="Enable detailed logging")
    
    # 输出配置
    output_dir: str = Field(default="output/animations", description="Output directory")
    temp_dir: str = Field(default="temp/animation_data", description="Temporary directory")
    
    # 子配置
    fbx_validation: FBXValidationConfig = Field(default_factory=FBXValidationConfig)
    quality_checker: QualityCheckerConfig = Field(default_factory=QualityCheckerConfig)
    mixamo: MixamoConfig = Field(default_factory=MixamoConfig)
    rigging_checker: RiggingCheckerConfig = Field(default_factory=RiggingCheckerConfig)
    transition_optimizer: TransitionOptimizerConfig = Field(default_factory=TransitionOptimizerConfig)
    animation_presets: AnimationPresetsConfig = Field(default_factory=AnimationPresetsConfig)
    
    class Config:
        env_prefix = "ENHANCED_FEATURES_"
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
enhanced_config = EnhancedFeaturesConfig()


def get_enhanced_config() -> EnhancedFeaturesConfig:
    """获取增强功能配置"""
    return enhanced_config


def update_config_from_dict(config_dict: Dict[str, Any]) -> None:
    """从字典更新配置"""
    global enhanced_config
    
    try:
        # 更新主配置
        for key, value in config_dict.items():
            if hasattr(enhanced_config, key):
                setattr(enhanced_config, key, value)
        
        logger.info("Configuration updated successfully")
        
    except Exception as e:
        logger.error(f"Failed to update configuration: {e}")
        raise


def save_config_to_file(filepath: str = "config/enhanced_features.json") -> None:
    """保存配置到文件"""
    try:
        import json
        from pathlib import Path
        
        # 确保目录存在
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        
        # 转换配置为字典
        config_dict = enhanced_config.dict()
        
        # 保存到文件
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(config_dict, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Configuration saved to: {filepath}")
        
    except Exception as e:
        logger.error(f"Failed to save configuration: {e}")
        raise


def load_config_from_file(filepath: str = "config/enhanced_features.json") -> None:
    """从文件加载配置"""
    try:
        import json
        
        if not os.path.exists(filepath):
            logger.warning(f"Configuration file not found: {filepath}")
            return
        
        with open(filepath, "r", encoding="utf-8") as f:
            config_dict = json.load(f)
        
        update_config_from_dict(config_dict)
        logger.info(f"Configuration loaded from: {filepath}")
        
    except Exception as e:
        logger.error(f"Failed to load configuration: {e}")
        raise


def validate_config() -> bool:
    """验证配置有效性"""
    try:
        config = get_enhanced_config()
        
        # 检查必要的目录
        os.makedirs(config.output_dir, exist_ok=True)
        os.makedirs(config.temp_dir, exist_ok=True)
        
        if config.enable_mixamo_integration and config.mixamo.cache_enabled:
            os.makedirs(config.mixamo.cache_dir, exist_ok=True)
        
        # 检查数值范围
        assert 0.0 <= config.quality_checker.min_quality_score <= 1.0
        assert config.fbx_validation.max_file_size_mb > 0
        assert config.mixamo.download_timeout > 0
        
        logger.info("Configuration validation passed")
        return True
        
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        return False


# 初始化时验证配置
if not validate_config():
    logger.warning("Configuration validation failed, using defaults")
