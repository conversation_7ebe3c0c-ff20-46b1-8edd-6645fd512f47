"""
任务管理 API 路由
Task Management API Routes
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, HTTPException, status
from loguru import logger

from ..models.task import (
    TaskResponse,
    TaskType,
    TaskStatus,
)
from ..services.task_service import TaskService

# 创建路由器
router = APIRouter(prefix="/tasks", tags=["Tasks"])

def get_task_service() -> TaskService:
    """获取任务服务"""
    return TaskService()


@router.get("/", response_model=List[TaskResponse])
async def list_tasks(
    user_id: Optional[str] = None,
    limit: int = 50
):
    """
    获取任务列表

    - **user_id**: 用户ID过滤（可选）
    - **limit**: 返回数量限制
    """
    try:
        task_service = get_task_service()
        tasks = await task_service.list_tasks_by_user(user_id=user_id, limit=limit)

        logger.info(f"Listed {len(tasks)} tasks")
        return tasks

    except Exception as e:
        logger.error(f"Failed to list tasks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list tasks: {str(e)}"
        )


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(task_id: str):
    """
    获取任务详情

    - **task_id**: 任务ID
    """
    try:
        task_service = get_task_service()
        task = await task_service.get_task(task_id)

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Task {task_id} not found"
            )

        return task

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get task {task_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get task: {str(e)}"
        )


@router.get("/health")
async def task_health_check():
    """任务系统健康检查"""
    logger.debug("Task health check endpoint accessed")

    try:
        task_service = get_task_service()

        # 尝试获取最近的任务
        recent_tasks = await task_service.list_tasks_by_user(limit=1)

        return {
            "status": "healthy",
            "task_service_available": True,
            "recent_tasks_count": len(recent_tasks),
            "database_connection": "ok",
        }

    except Exception as e:
        logger.exception(f"Error in task health check: {e}")
        return {
            "status": "error",
            "error_message": str(e),
            "task_service_available": False,
            "database_connection": "error",
        }



