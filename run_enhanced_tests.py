#!/usr/bin/env python3
"""
运行增强功能测试
Run Enhanced Features Tests
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger

# 配置日志
logger.remove()
logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO"
)
logger.add(
    "logs/enhanced_tests.log",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="DEBUG",
    rotation="10 MB"
)


async def main():
    """主函数"""
    logger.info("🎬 Motion Agent Enhanced Features Test Runner")
    logger.info("=" * 60)
    
    try:
        # 导入测试模块
        from test_enhanced_features import EnhancedFeaturesTest
        
        # 创建测试实例
        tester = EnhancedFeaturesTest()
        
        # 运行所有测试
        await tester.run_all_tests()
        
        logger.info("=" * 60)
        logger.success("🎉 All tests completed successfully!")
        
    except ImportError as e:
        logger.error(f"❌ Failed to import test modules: {e}")
        logger.info("💡 Make sure all dependencies are installed:")
        logger.info("   pip install -r requirements.txt")
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # 确保必要的目录存在
    os.makedirs("logs", exist_ok=True)
    os.makedirs("output/animations", exist_ok=True)
    os.makedirs("temp/animation_data", exist_ok=True)
    
    # 运行测试
    asyncio.run(main())
