#!/usr/bin/env python3
"""
增强功能测试脚本
Enhanced Features Test Script
测试所有新增的专业动画功能
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from backend.animation.fbx_validator import FBXValidator
from backend.animation.quality_checker import Animation<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from backend.animation.animation_presets import AnimationPresetsLibrary
from backend.animation.mixamo_integration import MixamoIntegration
from backend.animation.rigging_checker import Rigging<PERSON>uality<PERSON>hecker
from backend.animation.transition_optimizer import TransitionOptimizer


class EnhancedFeaturesTest:
    """增强功能测试类"""
    
    def __init__(self):
        self.test_results = {}
        
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 Starting Enhanced Features Test Suite")
        
        tests = [
            ("FBX Validator", self.test_fbx_validator),
            ("Quality Checker", self.test_quality_checker),
            ("Animation Presets", self.test_animation_presets),
            ("Mixamo Integration", self.test_mixamo_integration),
            ("Rigging Checker", self.test_rigging_checker),
            ("Transition Optimizer", self.test_transition_optimizer),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"🧪 Testing {test_name}...")
            try:
                result = await test_func()
                self.test_results[test_name] = {"status": "PASS", "result": result}
                logger.success(f"✅ {test_name} - PASSED")
            except Exception as e:
                self.test_results[test_name] = {"status": "FAIL", "error": str(e)}
                logger.error(f"❌ {test_name} - FAILED: {e}")
        
        self.generate_test_report()
    
    async def test_fbx_validator(self):
        """测试FBX验证器"""
        validator = FBXValidator()
        
        # 创建测试FBX文件（模拟）
        test_file = "output/animations/test_animation.fbx"
        os.makedirs("output/animations", exist_ok=True)
        
        # 创建一个简单的测试文件
        with open(test_file, "wb") as f:
            f.write(b"Kaydara FBX Binary  \x00\x1a\x00")  # FBX文件头
            f.write(b"\x00" * 100)  # 填充数据
        
        try:
            validation_result = validator.validate_fbx_file(test_file)
            
            # 验证结果结构
            assert "is_valid" in validation_result
            assert "file_exists" in validation_result
            assert "compatibility" in validation_result
            assert "quality_metrics" in validation_result
            
            # 生成报告
            report = validator.generate_validation_report(validation_result)
            assert len(report) > 0
            
            return {
                "validation_passed": True,
                "file_size": validation_result.get("file_size", 0),
                "compatibility_checks": len(validation_result.get("compatibility", {})),
                "recommendations_count": len(validation_result.get("recommendations", []))
            }
            
        finally:
            # 清理测试文件
            if os.path.exists(test_file):
                os.remove(test_file)
    
    async def test_quality_checker(self):
        """测试动画质量检查器"""
        checker = AnimationQualityChecker()
        
        # 创建测试动画数据
        test_animation_data = {
            "action_sequence": {
                "actions": [
                    {"type": "walk", "duration": 2.0},
                    {"type": "run", "duration": 1.5},
                    {"type": "jump", "duration": 1.8},
                    {"type": "land", "duration": 1.0}
                ],
                "frame_rate": 30,
                "total_duration": 6.3
            }
        }
        
        quality_result = checker.check_animation_quality(test_animation_data)
        
        # 验证结果结构
        assert "overall_score" in quality_result
        assert "quality_level" in quality_result
        assert "metrics" in quality_result
        assert "recommendations" in quality_result
        
        return {
            "overall_score": quality_result["overall_score"],
            "quality_level": quality_result["quality_level"],
            "issues_count": len(quality_result.get("issues", [])),
            "recommendations_count": len(quality_result.get("recommendations", []))
        }
    
    async def test_animation_presets(self):
        """测试动画预设库"""
        presets_library = AnimationPresetsLibrary()
        
        # 测试获取所有分类
        categories = presets_library.get_all_categories()
        assert len(categories) > 0
        
        # 测试搜索功能
        search_results = presets_library.search_presets(["walk", "run"])
        assert len(search_results) > 0
        
        # 测试按分类获取
        locomotion_presets = presets_library.get_presets_by_category("locomotion")
        assert len(locomotion_presets) > 0
        
        # 测试API导出
        api_presets = presets_library.export_presets_for_api()
        assert isinstance(api_presets, dict)
        
        return {
            "total_categories": len(categories),
            "total_presets": len(presets_library.presets),
            "search_results": len(search_results),
            "locomotion_presets": len(locomotion_presets)
        }
    
    async def test_mixamo_integration(self):
        """测试Mixamo集成"""
        integration = MixamoIntegration()
        
        # 测试动画映射
        mapping = integration.animation_mapping
        assert len(mapping) > 0
        
        # 测试动画建议
        suggestions = integration.get_animation_suggestions("walk forward and jump")
        assert len(suggestions) > 0
        
        # 测试可用分类
        categories = integration.get_available_categories()
        assert len(categories) > 0
        
        return {
            "mapping_categories": len(mapping),
            "suggestions_for_test": len(suggestions),
            "available_categories": len(categories),
            "cache_dir": integration.local_cache_dir
        }
    
    async def test_rigging_checker(self):
        """测试骨骼绑定检查器"""
        checker = RiggingQualityChecker()
        
        # 创建测试绑定数据
        test_rigging_data = {
            "bones": [
                {
                    "name": "root",
                    "parent": None,
                    "children": ["spine"],
                    "position": [0, 0, 0],
                    "rotation": [0, 0, 0],
                    "length": 1.0,
                    "weight_count": 10,
                    "has_constraints": False,
                    "is_deform_bone": True
                },
                {
                    "name": "spine",
                    "parent": "root",
                    "children": ["chest"],
                    "position": [0, 1, 0],
                    "rotation": [0, 0, 0],
                    "length": 1.5,
                    "weight_count": 15,
                    "has_constraints": True,
                    "is_deform_bone": True
                }
            ],
            "weights": {
                "vertex_0": {"root": 0.7, "spine": 0.3},
                "vertex_1": {"spine": 1.0}
            },
            "constraints": [
                {"type": "ik", "bone": "arm_l", "target": "hand_target_l"}
            ],
            "ik_chains": [
                {"name": "arm_l_ik", "bones": ["shoulder_l", "arm_l", "forearm_l"]}
            ]
        }
        
        rigging_result = checker.check_rigging_quality(test_rigging_data)
        
        # 验证结果结构
        assert "overall_score" in rigging_result
        assert "metrics" in rigging_result
        assert "issues" in rigging_result
        assert "recommendations" in rigging_result
        
        return {
            "overall_score": rigging_result["overall_score"],
            "total_bones": rigging_result["metrics"]["total_bones"],
            "issues_count": len(rigging_result["issues"]),
            "recommendations_count": len(rigging_result["recommendations"])
        }
    
    async def test_transition_optimizer(self):
        """测试动画过渡优化器"""
        optimizer = TransitionOptimizer()
        
        # 创建测试动作序列
        test_actions = [
            {"type": "idle", "duration": 2.0},
            {"type": "walk", "duration": 3.0},
            {"type": "run", "duration": 2.0},
            {"type": "jump", "duration": 1.5},
            {"type": "land", "duration": 1.0}
        ]
        
        # 测试序列优化
        optimized_sequence = optimizer.optimize_action_sequence(test_actions)
        assert len(optimized_sequence) >= len(test_actions)  # 应该包含过渡帧
        
        # 测试时间优化
        timing_optimized = optimizer.optimize_timing(test_actions)
        assert len(timing_optimized) == len(test_actions)
        
        # 测试过渡建议
        recommendations = optimizer.get_transition_recommendations("walk", "run")
        assert "compatibility_score" in recommendations
        assert "recommended_duration" in recommendations
        
        return {
            "original_actions": len(test_actions),
            "optimized_actions": len(optimized_sequence),
            "compatibility_score": recommendations["compatibility_score"],
            "recommended_type": recommendations["recommended_type"]
        }
    
    def generate_test_report(self):
        """生成测试报告"""
        logger.info("📊 Generating Test Report...")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result["status"] == "PASS")
        failed_tests = total_tests - passed_tests
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": f"{(passed_tests/total_tests)*100:.1f}%"
            },
            "details": self.test_results
        }
        
        # 保存报告到文件
        report_file = "test_results.json"
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        logger.info("📋 Test Summary:")
        logger.info(f"   Total Tests: {total_tests}")
        logger.info(f"   Passed: {passed_tests}")
        logger.info(f"   Failed: {failed_tests}")
        logger.info(f"   Success Rate: {report['summary']['success_rate']}")
        
        if failed_tests > 0:
            logger.warning("❌ Failed Tests:")
            for test_name, result in self.test_results.items():
                if result["status"] == "FAIL":
                    logger.warning(f"   - {test_name}: {result['error']}")
        
        logger.info(f"📄 Detailed report saved to: {report_file}")
        
        return report


async def main():
    """主函数"""
    logger.info("🎬 Motion Agent Enhanced Features Test")
    logger.info("=" * 50)
    
    tester = EnhancedFeaturesTest()
    await tester.run_all_tests()
    
    logger.info("=" * 50)
    logger.info("🏁 Test Suite Completed")


if __name__ == "__main__":
    asyncio.run(main())
